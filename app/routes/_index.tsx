import type { ActionFunctionArgs, MetaFunction } from "@remix-run/node";
import { Form, useLoaderData, useFetcher } from "@remix-run/react";
import { ChatDialogue } from "~/components/chat-dialogue";

import { streamText, tool } from 'ai';
import { openai } from '~/services/openai.server';
import { tools, toolMap } from '~/tools';
import { getAvailableModels } from '~/services/models.server';

export const meta: MetaFunction = () => {
  return [
    { title: "Chat Bot" },
    { name: "description", content: "A smart chat bot!" },
  ];
};

export async function loader() {
  console.log('Loader function called');
  const models = await getAvailableModels();
  console.log('Models fetched:', models.length);
  return Response.json({ models });
}

export async function action({ request }: ActionFunctionArgs) {
  console.log('Action function called');
  console.log('Request method:', request.method);
  // console.log('Request headers:', Object.fromEntries(request.headers.entries()));

  const { messages, model } = await request.json();
  console.log('Received messages:', messages, 'Model:', model);

  const stream = new ReadableStream({
    async start(controller) {
      const encoder = new TextEncoder();
      const sendJSON = (data: object) => {
        controller.enqueue(encoder.encode(`data: ${JSON.stringify(data)}`));
      };

      try {
        const result = await streamText({
          model: openai(model || 'gpt-4.1-nano'),
          messages,
          tools: tools.reduce((acc, t) => {
            acc[t.name] = tool({
              description: t.description,
              inputSchema: t.schema,
              execute: t.execute,
            });
            return acc;
          }, {} as any),
        });
        console.log('Result:', result);
        for await (const part of result.fullStream) {
          if (part.type === 'tool-call') {
            sendJSON({ type: 'tool_call', name: part.toolName, args: part.input });

            const tool = toolMap.get(part.toolName);
            if (!tool) {
              throw new Error(`Tool not found: ${part.toolName}`);
            }

            const toolResult = await tool.execute(part.input as { [x: string]: any });

            const finalResult = await streamText({
              model: openai(model || 'gpt-4o'),
              messages: [...messages, part, { type: 'tool-result', toolName: part.toolName, result: toolResult }],
            });

            for await (const finalPart of finalResult.fullStream) {
              if (finalPart.type === 'text-delta') {
                sendJSON({ type: 'text_chunk', content: finalPart.text });
              }
            }
          } else if (part.type === 'text-delta') {
            sendJSON({ type: 'text_chunk', content: part.text });
          }
        }
      } catch (e) {
        console.error(e);
        const errorPayload = { type: 'error', message: (e as Error).message };
        sendJSON(errorPayload);
      } finally {
        sendJSON({ type: 'end' });
        controller.close();
      }
    },
  });

  return new Response(stream, {
    headers: { 'Content-Type': 'text/event-stream', 'Cache-Control': 'no-cache' },
  });
}

export default function Index() {
  const { models } = useLoaderData<typeof loader>();

  return <ChatDialogue models={models} />;
}
