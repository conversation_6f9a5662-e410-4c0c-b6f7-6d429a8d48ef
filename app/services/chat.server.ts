import { streamText, tool } from 'ai';
import { openai } from './openai.server';
import { tools } from '~/tools';

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface ChatRequest {
  messages: ChatMessage[];
  model?: string;
}

export async function createChatStream(request: ChatRequest): Promise<ReadableStream> {
  const { messages, model } = request;
  console.log('Received messages:', messages, 'Model:', model);

  const stream = new ReadableStream({
    async start(controller) {
      const encoder = new TextEncoder();
      const sendJSON = (data: object) => {
        controller.enqueue(encoder.encode(`data: ${JSON.stringify(data)}\n\n`));
      };

      try {
        const result = await streamText({
          model: openai(model || 'gpt-4o-mini'),
          messages,
          tools: tools.reduce((acc, t) => {
            acc[t.name] = tool({
              description: t.description,
              inputSchema: t.schema,
              execute: t.execute,
            });
            return acc;
          }, {} as any),
        });

        console.log('StreamText result created, starting to process fullStream');

        for await (const part of result.fullStream) {
          console.log('Processing stream part:', part.type);

          if (part.type === 'tool-call') {
            console.log('Tool call detected:', part.toolName, part.input);
            sendJSON({ type: 'tool_call', name: part.toolName, args: part.input });
          } else if (part.type === 'tool-result') {
            console.log('Tool result received:', part.toolName);
            // Tool result is automatically handled by the AI SDK
          } else if (part.type === 'text-delta') {
            console.log('Text delta received:', part.text);
            sendJSON({ type: 'text_chunk', content: part.text });
          } else if (part.type === 'finish') {
            console.log('Stream finished with reason:', part.finishReason);
            break;
          } else if (part.type === 'error') {
            console.error('Stream error:', part.error);
            sendJSON({ type: 'error', message: String(part.error) });
            break;
          }
        }
      } catch (e) {
        console.error('Chat stream error:', e);
        const errorPayload = { type: 'error', message: (e as Error).message };
        sendJSON(errorPayload);
      } finally {
        sendJSON({ type: 'end' });
        controller.close();
      }
    },
  });

  return stream;
}
